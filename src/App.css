/* Google Keep Clone Styles */

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: '<PERSON>o', Arial, sans-serif;
  background-color: #ffffff;
  color: #202124;
}

/* Layout */
.home-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  display: flex;
  flex: 1;
}

/* Header Styles */
header {
  display: flex;
  align-items: center;
  padding: 8px 10px 8px 16px; /* Added 10px right padding as requested */
  height: 64px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

header section {
  display: flex;
  align-items: center;
}

/* Removed the section:first-child styling as we're now using header-brand-container */

header section:nth-child(2) {
  flex: 1;
}

header section:last-child {
  flex: 0 0 auto;
}

header a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #5f6368;
  cursor: pointer;
}

header img {
  height: 40px;
  margin-right: 8px;
  cursor: pointer;
}

header span {
  font-size: 22px;
  color: #5f6368;
}

/* Add cursor pointer to header elements */
header a,
header a img,
header a span {
  cursor: pointer;
}

/* Header brand container styling */
.header-brand-container {
  display: flex;  
  align-items: center;
  width: 202px;
  padding-right: 30px; /* Add padding to the right side only */
  margin-left: 0;
}

/* Sidebar Styles */
.sidebar {
  transition: width 0.1s cubic-bezier(0.4, 0.0, 0.2, 1);
  width: 72px; /* Default collapsed width */
  height: calc(100vh - 64px - 16px); /* Account for header + top margin */
  overflow: hidden;
  border-right: none;
  position: relative;
  margin-top: 10px; /* Add space between header and sidebar */
}

.sidebar.expanded {
  width: 280px; /* Expanded width */
}

/* Fixed icon zone - always 72px wide */
.sidebar-buttons {
  display: flex;
  flex-direction: column;
  position: relative;
}

.sidebar-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 48px;
  position: relative;
  margin: 0px 0;
}

/* Fixed icon area - never moves */
.sidebar-button .icon-wrapper {
  position: absolute;
  left: 12px;
  top: 0;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

/* Expandable background area */
.sidebar-button .button-background {
  position: absolute;
  left: 12px;
  top: 0;
  height: 48px;
  width: 48px;
  border-radius: 50%;
  transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.sidebar.expanded .sidebar-button .button-background {
  left: 0;
  width: 256px;
  border-radius: 0 25px 25px 0;
  margin-right: 8px;
}

.sidebar-button:hover .button-background {
  background-color: #f1f3f4;
}

/* Text area - only visible when expanded */
.sidebar-button .button-name {
  position: absolute;
  left: 72px;
  font-size: 14px;
  color: #202124;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
  z-index: 1;
}

.sidebar.expanded .sidebar-button .button-name {
  opacity: 1;
}

/* Icon styling */
.sidebar-button .material-symbols-outlined {
  color: #5f6368;
  font-size: 24px;
}

/* Active button styles */
.sidebar-button.active .button-background {
  background-color: #feefc3;
}

.sidebar-button.active .material-symbols-outlined {
  color: black;
  font-variation-settings:
  'FILL' 1,
  'wght' 400,
  'GRAD' 0,
  'opsz' 24;
}

/* Notes Container */
.notes-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px; /* Add space between header and main content */
}

/* Notes List */
.notes-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  width: 100%;
  max-width: 1200px;
  margin-top: 20px;
}

/* Note Item */
.note {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.302), 0 1px 3px 1px rgba(60, 64, 67, 0.149);
  transition: box-shadow 0.2s ease;
}

.note:hover {
  box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.302), 0 4px 8px 3px rgba(60, 64, 67, 0.149);
}

.note .note-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.note .note-content {
  font-size: 14px;
  flex-grow: 1;
  margin-bottom: 8px;
}

.note .note-actions {
  display: flex;
  justify-content: flex-start;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.note:hover .note-actions {
  opacity: 1;
}

.note-icons {
  display: flex;
  gap: 12px;
}

.note-icons .material-symbols-outlined {
  font-size: 18px;
  color: #5f6368;
}

/* Note Taker Styles */
.note-taker {
  width: 600px;
  max-width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.302), 0 2px 6px 2px rgba(60, 64, 67, 0.149);
  margin-bottom: 20px;
  background-color: #fff;
  transition: box-shadow 0.2s ease;
}

.note-taker:focus-within {
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
}

.note-taker .note-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  outline: none;
  min-height: 20px;
  border: none;
  width: 100%;
}

.note-taker .note-content {
  font-size: 14px;
  outline: none;
  min-height: 20px;
  border: none;
  width: 100%;
  margin-bottom: 8px;
}

.note-taker .note-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.note-taker .note-icons {
  display: flex;
  gap: 16px;
}

.note-taker .note-icons .material-symbols-outlined {
  font-size: 18px;
  color: #5f6368;
  cursor: pointer;
}

.note-taker .note-icons .material-symbols-outlined:hover {
  color: #202124;
}

.note-taker button {
  background-color: transparent;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #5f6368;
  font-weight: 500;
}

.note-taker button:hover {
  background-color: #f1f3f4;
  color: #202124;
}

/* Search Bar Styles */
.search-bar {
  position: relative;
  width: 721px; /* Corrected width as specified */
  height: 47px; /* Corrected height as specified */
}

.search-bar .search-icon {
  position: absolute;
  left: 16px; /* Position icon without affecting input padding */
  top: 50%;
  transform: translateY(-50%);
  color: #5f6368;
  z-index: 1;
}

input[type="text"] {
  width: 100%;
  height: 100%;
  padding: 0 0 0 50px; /* Increased left padding for more space between icon and text */
  border: none;
  border-radius: 8px;
  background-color: #f1f3f4;
  font-size: 16px;
  outline: none;
}

/* Action Area Styles */
.action-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px; /* Space between the two groups */
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 16px; /* Space between icons within a group */
}

.action-area .material-symbols-outlined {
  color: #5f6368;
  font-size: 24px;
  padding: 12px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.action-area .material-symbols-outlined:hover {
  background-color: #e9edef;
}

/* Material Icons Styling */
.material-symbols-outlined {
  font-variation-settings:
  'FILL' 0,
  'wght' 400,
  'GRAD' 0,
  'opsz' 24;
  cursor: pointer;
}

/* Profile Picture Styling */
.profile-pic-container {
  position: relative;
  width: 36px;  /* Decreased from 48px */
  height: 36px; /* Decreased from 48px */
  border-radius: 50%;
  cursor: pointer;
}

.profile-pic {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  position: relative;
  z-index: 1;
}

.google-colors-border {
  position: absolute;
  top: -3px;    /* Adjusted for smaller profile picture */
  left: -3px;   /* Adjusted for smaller profile picture */
  right: -3px;  /* Adjusted for smaller profile picture */
  bottom: -3px; /* Adjusted for smaller profile picture */
  border-radius: 50%;
  background: conic-gradient(
    #4285F4, /* Google Blue */
    #34A853, /* Google Green */
    #FBBC05, /* Google Yellow */
    #EA4335, /* Google Red */
    #4285F4  /* Back to Blue */
  );
  z-index: 0;
}

/* Menu Icon Hover Effect */
.menu-icon {
  padding: 12px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  margin-right: 0px; /* Reduced space between menu icon and logo */
  outline: none; /* Remove the default focus outline */
}

.menu-icon:hover {
  background-color: #e9edef;
}

/* Style for active menu state */
.menu-icon.menu-active {
  background-color: #e9edef;
}

/* Placeholder styling */
[contenteditable=true]:empty:before {
  content: attr(placeholder);
  color: #aaa;
  font-style: italic;
}
